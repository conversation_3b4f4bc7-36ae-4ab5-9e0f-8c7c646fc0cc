import { logging } from "@skywind-group/sw-utils";
import { Notificator, EmptyPoolAlert, EntityDomainBlockedAlert } from "./types";
import { getEmailService } from "../email";
import { EmailData } from "../../utils/emails";
import config from "../../config";

export class EmailNotificator implements Notificator {
    private readonly log: logging.Logger;

    constructor(log: logging.Logger) {
        this.log = log;
    }

    public async alertEmptyPool(alert: EmptyPoolAlert): Promise<void> {
        const subject = `Empty Domain Pool Alert - ${alert.poolName}`;
        const body = `
Domain Pool Alert

Pool: ${alert.poolName} (ID: ${alert.poolId})
Pool Type: ${alert.poolType}
Impact: No domains available for new assignments
Action Required: Add more domains to this pool or check domain statuses

This is an automated alert from the Domain Monitor system.
        `.trim();

        await this.sendEmail(subject, body);
    }

    public async alertEntityDomainBlocked(alert: EntityDomainBlockedAlert): Promise<void> {
        const timestamp = alert.blockedAt.toISOString();
        const subject = `Domain Blocked Alert - ${alert.domain}`;
        const body = `
Default Domain Blocked Alert

Domain: ${alert.domain}
Domain ID: ${alert.domainId}
Domain Type: ${alert.staticType}
Blocked At: ${timestamp}
Impact: Default domain is no longer accessible

This is an automated alert from the Domain Monitor system.
        `.trim();

        await this.sendEmail(subject, body);
    }

    private async sendEmail(subject: string, body: string): Promise<void> {
        const { enabled, recipients } = config.domainMonitoring.emailNotifications;

        if (!enabled) {
            this.log.debug({ subject, body }, "Email notifications disabled, skipping email send");
            return;
        }

        if (!recipients || recipients.length === 0) {
            this.log.warn({ subject, body }, "No email recipients configured for domain monitoring alerts");
            return;
        }

        try {
            const emailService = getEmailService();
            const emailData: EmailData = {
                subject,
                htmlPart: `<html><body><pre>${body}</pre></body></html>`
            };

            await emailService.sendEmail(recipients, emailData);
            this.log.info({ subject, recipients: recipients.length }, "Domain monitoring email notification sent successfully");
        } catch (error) {
            this.log.error({ error, subject, recipients }, "Failed to send domain monitoring email notification");
            throw error;
        }
    }
}
