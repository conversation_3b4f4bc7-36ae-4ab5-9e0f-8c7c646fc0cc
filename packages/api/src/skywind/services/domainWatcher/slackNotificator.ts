import { SlackService, SlackMessage } from "../../utils/slackService";
import { logging } from "@skywind-group/sw-utils";
import config from "../../config";
import { Notificator, EmptyPoolAlert, EntityDomainBlockedAlert } from "./types";

export class SlackNotificator implements Notificator {
    private readonly slackService: SlackService;

    constructor() {
        const log = logging.logger("slack-service");
        this.slackService = new SlackService(
            config.slackNotifications.blockedDomainsWebhookUrl,
            {
                timeout: config.slackNotifications.timeout,
                retryConfig: config.slackNotifications.retryConfig,
                headers: {
                    "Content-Type": "application/json"
                }
            },
            log
        );
    }

    public async alertEmptyPool(alert: EmptyPoolAlert): Promise<void> {
        let text = "⚠️ *Empty Domain Pool*\n";
        text += `*Pool:* ${alert.poolName} (ID: ${alert.poolId})\n`;
        text += `*Pool Type:* ${alert.poolType}\n`;

        const message: SlackMessage = {
            text,
            channel: "#blocked_domains",
            username: "Domain Monitor",
            icon_emoji: ":exclamation:"
        };

        await this.slackService.sendMessage(message);
    }

    public async alertEntityDomainBlocked(alert: EntityDomainBlockedAlert): Promise<void> {
        const timestamp = alert.blockedAt.toISOString();
        let text = "🔴 *Default Domain Blocked Alert*\n";
        text += `*Domain:* ${alert.domain}\n`;
        text += `*Domain Type:* ${alert.staticType}\n`;
        text += `*Blocked At:* ${timestamp}\n`;
        text += "*Impact:* Default domain is no longer accessible\n";

        const message: SlackMessage = {
            text,
            channel: "#blocked_domains",
            username: "Domain Monitor",
            icon_emoji: ":rotating_light:"
        };

        await this.slackService.sendMessage(message);
    }
}
