import { StaticDomainType } from "../../entities/domain";

export interface DomainSource {
    readonly type: "staticPool" | "dynamicPool" | "entity";
    readonly staticType?: StaticDomainType;
    readonly domainId: number;
    readonly poolId?: number;
    readonly poolName?: string;
    readonly environment?: string;
    readonly entityId: number;
    readonly entityName: string;
    readonly entityPath: string;
}

export type AdapterDomains = Map<string, DomainSources>;
export type DomainSources = Map<string, { domainId: number; sources: DomainSource[] }>;

export type AccessStatus = "AVAILABLE" | "BLOCKED" | "UNKNOWN";

export interface DomainStatus {
    accessStatus: AccessStatus;
    lastCheckedAt: Date;
}

export interface MonitoredDomain {
    domain: string;
    status: DomainStatus;
}

export interface Region {
    countryCode: string;
}

export interface DomainWatcherAdapter {
    adapterId: string;
    register(domain: string): Promise<MonitoredDomain>;
    remove(domain: string): Promise<void>;
    get(domain: string): Promise<MonitoredDomain>;
    list(): Promise<MonitoredDomain[]>;
}

export interface EmptyPoolAlert {
    poolId: number;
    poolName: string;
    poolType: "static" | "dynamic";
}

export interface EntityDomainBlockedAlert {
    domainId: number;
    domain: string;
    blockedAt: Date;
    staticType: StaticDomainType;
}

export interface Notificator {
    alertEmptyPool(alert: EmptyPoolAlert): Promise<void>;
    alertEntityDomainBlocked(alert: EntityDomainBlockedAlert): Promise<void>;
}
