import { SlackNotificator } from "./slackNotificator";
import { EmailNotificator } from "./emailNotificator";
import { Notificator, EmptyPoolAlert, EntityDomainBlockedAlert } from "./types";

export class CombineNotificator implements Notificator {
    private readonly notificators: Notificator[];

    constructor(notificators?: Notificator[]) {
        this.notificators = notificators || [
            new SlackNotificator(),
            new EmailNotificator()
        ];
    }

    public async alertEmptyPool(alert: EmptyPoolAlert): Promise<void> {
        for (const notificator of this.notificators) {
            await notificator.alertEmptyPool(alert);
        }
    }

    public async alertEntityDomainBlocked(alert: EntityDomainBlockedAlert): Promise<void> {
        for (const notificator of this.notificators) {
            await notificator.alertEntityDomainBlocked(alert);
        }
    }
}
