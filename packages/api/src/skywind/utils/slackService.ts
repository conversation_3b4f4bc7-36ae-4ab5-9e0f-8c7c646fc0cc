import { logging } from "@skywind-group/sw-utils";
import { createHttpClient, HttpClient, HttpClientConfig } from "./httpClient";

export interface SlackMessage {
    text: string;
    channel?: string;
    username?: string;
    icon_emoji?: string;
}

export class SlackService {
    private readonly httpClient: HttpClient;
    private readonly webhookUrl: string;

    constructor(webhookUrl: string, httpClientConfig: HttpClientConfig, log?: logging.Logger) {
        this.webhookUrl = webhookUrl;

        this.httpClient = createHttpClient(httpClientConfig, log);
    }

    public async sendMessage(message: SlackMessage): Promise<void> {
        await this.httpClient.post(this.webhookUrl, message);
    }
}
