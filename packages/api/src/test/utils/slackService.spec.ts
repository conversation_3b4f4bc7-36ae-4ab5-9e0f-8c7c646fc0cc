import { SinonStub, stub, restore } from "sinon";
import { expect, should } from "chai";
import { SlackService, SlackMessage } from "../../skywind/utils/slackService";
import { HttpClientConfig } from "../../skywind/utils/httpClient";
import * as httpClientModule from "../../skywind/utils/httpClient";
import config from "../../skywind/config";

should();

class MockLogger {
    info(message: any, text?: string) {}
    debug(message: any, text?: string) {}
    warn(message: any, text?: string) {}
    error(message: any, text?: string) {}
}

class MockHttpClient {
    private sentMessages: any[] = [];

    async post<T>(url: string, data: any): Promise<T> {
        this.sentMessages.push({ url, data });
        return {} as T;
    }

    // Helper methods for testing
    getSentMessages() {
        return this.sentMessages;
    }

    getLastMessage() {
        return this.sentMessages[this.sentMessages.length - 1];
    }

    clear() {
        this.sentMessages = [];
    }
}

describe("SlackService", () => {
    let createHttpClientStub: SinonStub;
    let httpClient: MockHttpClient;
    let slackService: SlackService;
    let mockLogger: MockLogger;
    let originalConfig: any;
    let testHttpClientConfig: HttpClientConfig;

    before(() => {
        originalConfig = {
            webhookUrl: config.slackNotifications.blockedDomainsWebhookUrl
        };

        config.slackNotifications.blockedDomainsWebhookUrl = "https://hooks.slack.com/test";

        testHttpClientConfig = {
            timeout: config.slackNotifications.timeout,
            retryConfig: config.slackNotifications.retryConfig,
            headers: {
                "Content-Type": "application/json"
            }
        };
    });

    after(() => {
        config.slackNotifications.blockedDomainsWebhookUrl = originalConfig.webhookUrl;
    });

    beforeEach(() => {
        createHttpClientStub = stub(httpClientModule, "createHttpClient");
        httpClient = new MockHttpClient();
        mockLogger = new MockLogger();
        createHttpClientStub.returns(httpClient);
        slackService = new SlackService(
            config.slackNotifications.blockedDomainsWebhookUrl,
            testHttpClientConfig,
            mockLogger
        );
    });

    afterEach(() => {
        restore();
        httpClient.clear();
    });

    describe("constructor", () => {
        it("should create http client with provided config", () => {
            const customConfig: HttpClientConfig = {
                timeout: 5000,
                retryConfig: {
                    retries: 2,
                    retryDelay: 1000
                },
                headers: {
                    "Content-Type": "application/json"
                }
            };

            // Reset the stub to track this specific call
            createHttpClientStub.resetHistory();

            new SlackService("https://test.webhook.url", customConfig, mockLogger);

            expect(createHttpClientStub.calledOnce).to.be.true;
            expect(createHttpClientStub.firstCall.args[0]).to.deep.equal(customConfig);
        });
    });

    describe("sendMessage", () => {
        it("should send message", async () => {
            const message: SlackMessage = {
                text: "Test message",
                channel: "#test",
                username: "Test Bot",
                icon_emoji: ":test:"
            };

            await slackService.sendMessage(message);

            const sentMessages = httpClient.getSentMessages();
            expect(sentMessages).to.have.length(1);
            expect(sentMessages[0].url).to.equal("https://hooks.slack.com/test");
            expect(sentMessages[0].data).to.deep.equal(message);
        });

        it("should handle http client errors", async () => {
            const errorHttpClient = {
                post: stub().rejects(new Error("Network error"))
            };
            createHttpClientStub.returns(errorHttpClient);

            const errorService = new SlackService(
                "https://hooks.slack.com/test",
                testHttpClientConfig,
                mockLogger
            );

            const message: SlackMessage = {
                text: "Test message",
                channel: "#test",
                username: "Test Bot",
                icon_emoji: ":test:"
            };

            try {
                await errorService.sendMessage(message);
                expect.fail("Should have thrown an error");
            } catch (error) {
                expect(error.message).to.equal("Network error");
            }
        });
    });

    describe("when webhook URL is empty", () => {
        it("should still attempt to send message", async () => {
            const serviceWithoutUrl = new SlackService("", testHttpClientConfig, mockLogger);

            const message: SlackMessage = {
                text: "Test message",
                channel: "#test",
                username: "Test Bot",
                icon_emoji: ":test:"
            };

            await serviceWithoutUrl.sendMessage(message);

            const sentMessages = httpClient.getSentMessages();
            expect(sentMessages).to.have.length(1);
            expect(sentMessages[0].url).to.equal("");
            expect(sentMessages[0].data).to.deep.equal(message);
        });
    });
});
