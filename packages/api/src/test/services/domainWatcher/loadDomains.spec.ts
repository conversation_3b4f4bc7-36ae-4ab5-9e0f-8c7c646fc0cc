import { expect } from "chai";
import { loadDomains } from "../../../skywind/services/domainWatcher/loadDomains";
import { truncate } from "../../entities/helper";
import { FACTORY } from "../../factories/common";
import { EntityStatus } from "../../../skywind/entities/entity";
import { Models } from "../../../skywind/models/models";

const factory = require("factory-girl").factory;

describe("loadDomains", () => {
    beforeEach(async () => {
        await truncate();
    });

    it("should load domains from static domain pools", async () => {
        const staticPoolDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static-pool-domain.com"
        });
        const staticEntityDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static-entity-domain.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-static-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: staticPoolDomain.id,
            isActive: true
        });

        await factory.create(FACTORY.ENTITY, {
            name: "test-entity",
            path: ":test-entity:",
            status: EntityStatus.NORMAL,
            staticDomainId: staticEntityDomain.id,
            staticDomainPoolId: staticDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("static-pool-domain.com")).to.be.true;
        expect(tapkingDomains?.has("static-entity-domain.com")).to.be.true;
    });

    it("should load domains from dynamic domain pools", async () => {
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "dynamic-pool-domain.com",
            environment: "test"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-dynamic-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: dynamicDomain.id,
            isActive: true
        });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            dynamicDomainPoolId: dynamicDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("dynamic-pool-domain.com")).to.be.true;
    });

    it("should load individual dynamic domains with pool adapter", async () => {
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "individual-dynamic-domain.com",
            environment: "test"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-pool-for-adapter",
            domainWatcherAdapterId: "custom-adapter"
        });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            dynamicDomainId: dynamicDomain.id,
            dynamicDomainPoolId: dynamicDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("custom-adapter")).to.be.true;

        const customAdapterDomains = result.get("custom-adapter");
        expect(customAdapterDomains).to.be.instanceOf(Map);
        expect(customAdapterDomains?.has("individual-dynamic-domain.com")).to.be.true;
    });

    it("should not load individual dynamic domains without pool adapter", async () => {
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "orphaned-dynamic-domain.com",
            environment: "test"
        });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            dynamicDomainId: dynamicDomain.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.size).to.equal(0);
    });

    it("should handle mixed static and dynamic domains", async () => {
        const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "mixed-static-domain.com"
        });

        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "mixed-dynamic-domain.com",
            environment: "test"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-mixed-pool",
            domainWatcherAdapterId: "tapking"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-mixed-pool",
            domainWatcherAdapterId: "tapking"
        });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            staticDomainId: staticDomain.id,
            dynamicDomainId: dynamicDomain.id,
            staticDomainPoolId: staticDomainPool.id,
            dynamicDomainPoolId: dynamicDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("mixed-static-domain.com")).to.be.true;
        expect(tapkingDomains?.has("mixed-dynamic-domain.com")).to.be.true;
    });

    it("should skip inactive domains from pools", async () => {
        const activeDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "active-dynamic-domain.com",
            environment: "test"
        });
        const inactiveDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "inactive-dynamic-domain.com",
            environment: "test"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-active-inactive-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: activeDomain.id,
            isActive: true
        });
        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: inactiveDomain.id,
            isActive: false
        });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            dynamicDomainPoolId: dynamicDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("active-dynamic-domain.com")).to.be.true;
        expect(tapkingDomains?.has("inactive-dynamic-domain.com")).to.be.false;
    });

    it("should return empty map when no entities found", async () => {
        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.size).to.equal(0);
    });

    it("should include environment in domain sources", async () => {
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "test-env-domain.com",
            environment: "test-env"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            domainWatcherAdapterId: "tapking"
        });

        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: dynamicDomain.id,
            isActive: true
        });

        const entity = await factory.create(FACTORY.ENTITY, {
            name: "test-env-entity",
            path: ":test-env-entity:",
            status: EntityStatus.NORMAL,
            dynamicDomainPoolId: dynamicDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("test-env-domain.com")).to.be.true;

        const domainInfo = tapkingDomains?.get("test-env-domain.com");
        expect(domainInfo?.sources).to.have.length(1);
        expect(domainInfo?.sources[0].environment).to.equal("test-env"); // Only domain environment is used
    });

    it("should include poolName in domain sources", async () => {
        const staticPoolDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static-pool-domain.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-static-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: staticPoolDomain.id,
            isActive: true
        });

        const entity = await factory.create(FACTORY.ENTITY, {
            name: "test-static-entity",
            path: ":test-static-entity:",
            status: EntityStatus.NORMAL,
            staticDomainPoolId: staticDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("static-pool-domain.com")).to.be.true;

        const domainInfo = tapkingDomains?.get("static-pool-domain.com");
        expect(domainInfo?.sources).to.have.length(1);
        expect(domainInfo?.sources[0].poolName).to.equal("test-static-pool");
        expect(domainInfo?.sources[0].type).to.equal("staticPool");
        expect(domainInfo?.sources[0].entityId).to.equal(entity.id);
        expect(domainInfo?.sources[0].entityName).to.equal("test-static-entity");
        expect(domainInfo?.sources[0].entityPath).to.equal(":test-static-entity:");
    });

    it("should include poolName in dynamic domain pool sources", async () => {
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "dynamic-pool-domain.com",
            environment: "test-env"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-dynamic-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: dynamicDomain.id,
            isActive: true
        });

        const entity = await factory.create(FACTORY.ENTITY, {
            name: "test-dynamic-entity",
            path: ":test-dynamic-entity:",
            status: EntityStatus.NORMAL,
            dynamicDomainPoolId: dynamicDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("dynamic-pool-domain.com")).to.be.true;

        const domainInfo = tapkingDomains?.get("dynamic-pool-domain.com");
        expect(domainInfo?.sources).to.have.length(1);
        expect(domainInfo?.sources[0].poolName).to.equal("test-dynamic-pool");
        expect(domainInfo?.sources[0].type).to.equal("dynamicPool");
        expect(domainInfo?.sources[0].environment).to.equal("test-env");
        expect(domainInfo?.sources[0].entityId).to.equal(entity.id);
        expect(domainInfo?.sources[0].entityName).to.equal("test-dynamic-entity");
        expect(domainInfo?.sources[0].entityPath).to.equal(":test-dynamic-entity:");
    });

    it("should include entity information in all domain sources", async () => {
        const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "entity-test-domain.com"
        });

        const entity = await factory.create(FACTORY.ENTITY, {
            name: "entity-info-test",
            path: ":entity-info-test:",
            status: EntityStatus.NORMAL,
            staticDomainId: staticDomain.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.size).to.equal(0); // No domain watcher adapter configured, so no results

        // Test with a static domain pool that has domain watcher adapter
        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "entity-test-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: staticDomain.id,
            isActive: true
        });

        const entityWithPool = await factory.create(FACTORY.ENTITY, {
            name: "entity-with-pool",
            path: ":entity-with-pool:",
            status: EntityStatus.NORMAL,
            staticDomainPoolId: staticDomainPool.id
        });

        const resultWithPool = await loadDomains();

        expect(resultWithPool).to.be.instanceOf(Map);
        expect(resultWithPool.has("tapking")).to.be.true;

        const tapkingDomains = resultWithPool.get("tapking");
        expect(tapkingDomains?.has("entity-test-domain.com")).to.be.true;

        const domainInfo = tapkingDomains?.get("entity-test-domain.com");
        expect(domainInfo?.sources).to.have.length(1);

        const source = domainInfo?.sources[0];
        expect(source?.entityId).to.equal(entityWithPool.id);
        expect(source?.entityName).to.equal("entity-with-pool");
        expect(source?.entityPath).to.equal(":entity-with-pool:");
        expect(source?.type).to.equal("staticPool");
        expect(source?.poolName).to.equal("entity-test-pool");
    });
});
