import { expect } from "chai";
import * as sinon from "sinon";
import { EmailNotificator } from "../../../skywind/services/domainWatcher/emailNotificator";
import { EmptyPoolAlert, EntityDomainBlockedAlert } from "../../../skywind/services/domainWatcher/types";
import { StaticDomainType } from "../../../skywind/entities/domain";
import * as emailService from "../../../skywind/services/email";
import config from "../../../skywind/config";

describe("EmailNotificator", () => {
    let notificator: EmailNotificator;
    let sendEmailStub: sinon.SinonStub;
    let originalConfig: any;

    beforeEach(() => {
        notificator = new EmailNotificator();
        sendEmailStub = sinon.stub();
        sinon.stub(emailService, "getEmailService").returns({
            sendEmail: sendEmailStub
        });

        // Store original config
        originalConfig = { ...config.domainMonitoring.emailNotifications };
    });

    afterEach(() => {
        sinon.restore();
        // Restore original config
        config.domainMonitoring.emailNotifications = originalConfig;
    });

    describe("alertEmptyPool", () => {
        it("should send email when notifications are enabled", async () => {
            // Arrange
            config.domainMonitoring.emailNotifications.enabled = true;
            config.domainMonitoring.emailNotifications.recipients = ["<EMAIL>"];

            const alert: EmptyPoolAlert = {
                poolId: 123,
                poolName: "Test Pool",
                poolType: "static"
            };

            sendEmailStub.resolves();

            // Act
            await notificator.alertEmptyPool(alert);

            // Assert
            expect(sendEmailStub.calledOnce).to.be.true;
            const [recipients, emailData] = sendEmailStub.firstCall.args;
            expect(recipients).to.deep.equal(["<EMAIL>"]);
            expect(emailData.subject).to.equal("Empty Domain Pool Alert - Test Pool");
            expect(emailData.htmlPart).to.include("Test Pool");
            expect(emailData.htmlPart).to.include("ID: 123");
            expect(emailData.htmlPart).to.include("Pool Type: static");
        });

        it("should not send email when notifications are disabled", async () => {
            // Arrange
            config.domainMonitoring.emailNotifications.enabled = false;
            config.domainMonitoring.emailNotifications.recipients = ["<EMAIL>"];

            const alert: EmptyPoolAlert = {
                poolId: 123,
                poolName: "Test Pool",
                poolType: "static"
            };

            // Act
            await notificator.alertEmptyPool(alert);

            // Assert
            expect(sendEmailStub.called).to.be.false;
        });

        it("should not send email when no recipients configured", async () => {
            // Arrange
            config.domainMonitoring.emailNotifications.enabled = true;
            config.domainMonitoring.emailNotifications.recipients = [];

            const alert: EmptyPoolAlert = {
                poolId: 123,
                poolName: "Test Pool",
                poolType: "static"
            };

            // Act
            await notificator.alertEmptyPool(alert);

            // Assert
            expect(sendEmailStub.called).to.be.false;
        });

        it("should handle email sending errors", async () => {
            // Arrange
            config.domainMonitoring.emailNotifications.enabled = true;
            config.domainMonitoring.emailNotifications.recipients = ["<EMAIL>"];

            const alert: EmptyPoolAlert = {
                poolId: 123,
                poolName: "Test Pool",
                poolType: "static"
            };

            const error = new Error("Email sending failed");
            sendEmailStub.rejects(error);

            // Act & Assert
            try {
                await notificator.alertEmptyPool(alert);
                expect.fail("Should have thrown an error");
            } catch (err) {
                expect(err).to.equal(error);
            }
        });
    });

    describe("alertEntityDomainBlocked", () => {
        it("should send email when notifications are enabled", async () => {
            // Arrange
            config.domainMonitoring.emailNotifications.enabled = true;
            config.domainMonitoring.emailNotifications.recipients = ["<EMAIL>", "<EMAIL>"];

            const blockedAt = new Date("2023-10-08T10:30:00Z");
            const alert: EntityDomainBlockedAlert = {
                domainId: 456,
                domain: "blocked-domain.com",
                blockedAt,
                staticType: StaticDomainType.DEFAULT
            };

            sendEmailStub.resolves();

            // Act
            await notificator.alertEntityDomainBlocked(alert);

            // Assert
            expect(sendEmailStub.calledOnce).to.be.true;
            const [recipients, emailData] = sendEmailStub.firstCall.args;
            expect(recipients).to.deep.equal(["<EMAIL>", "<EMAIL>"]);
            expect(emailData.subject).to.equal("Domain Blocked Alert - blocked-domain.com");
            expect(emailData.htmlPart).to.include("blocked-domain.com");
            expect(emailData.htmlPart).to.include("Domain ID: 456");
            expect(emailData.htmlPart).to.include("2023-10-08T10:30:00.000Z");
            expect(emailData.htmlPart).to.include("Domain Type: DEFAULT");
        });

        it("should not send email when notifications are disabled", async () => {
            // Arrange
            config.domainMonitoring.emailNotifications.enabled = false;
            config.domainMonitoring.emailNotifications.recipients = ["<EMAIL>"];

            const alert: EntityDomainBlockedAlert = {
                domainId: 456,
                domain: "blocked-domain.com",
                blockedAt: new Date(),
                staticType: StaticDomainType.DEFAULT
            };

            // Act
            await notificator.alertEntityDomainBlocked(alert);

            // Assert
            expect(sendEmailStub.called).to.be.false;
        });
    });

    describe("sendEmail", () => {
        it("should send multiple recipients correctly", async () => {
            // Arrange
            config.domainMonitoring.emailNotifications.enabled = true;
            config.domainMonitoring.emailNotifications.recipients = [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
            ];

            const alert: EmptyPoolAlert = {
                poolId: 789,
                poolName: "Multi Recipient Pool",
                poolType: "dynamic"
            };

            sendEmailStub.resolves();

            // Act
            await notificator.alertEmptyPool(alert);

            // Assert
            expect(sendEmailStub.calledOnce).to.be.true;
            const [recipients] = sendEmailStub.firstCall.args;
            expect(recipients).to.have.length(3);
            expect(recipients).to.include("<EMAIL>");
            expect(recipients).to.include("<EMAIL>");
            expect(recipients).to.include("<EMAIL>");
        });
    });
});
